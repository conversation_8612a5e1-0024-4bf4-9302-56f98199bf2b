let images = [];
let currentStage = -2; // -2:初始文字介绍 -1:六行文字选择 0-5:六张图片
let particles = [];
let blocks = [];
let transitioning = false;
let fadeAmount = 0;
let fading = false;
let nextStageReady = false;
let density = 6;
let blockAssembled = false;
let fadeOut = false;
let fadeOutAlpha = 0;
let blockClickedOnce = false;


// 初始文本介绍
let textLines = ["Whispers of Memory", "Do you still remember your childhood memory....", "秋山亮二摄影集"];
let textAlpha = [0, 0, 0];
let textFadeSpeed = 3;
let currentTextLine = 0;
let textIntroComplete = false;
let nextLineTime = 0;
let textFadingOut = false;

// 六行选择文字
let menuLines = [
  "Memory 1: The Kingdom of Toys",
  "Memory 2: White Dress Dream on the Grass", 
  "Memory 3: Eyes Behind the Door",
  "Memory 4: Run, Candy Girl",
  "Memory 5: Summer of Ice Cream and Friendship",
  "Memory 6: Us in the Classroom"
];

// 图片底部文字
let imageTexts = [
  "Her world began to expand with a single toy.",
  "On a quiet afternoon, she became a drifting cloud.",
  "She peeks out—the world begins to have edges.",
  "Sweetness on her lips, sunshine in her heart.",
  "The sweetest thing isn't just the ice cream—it's their laughter.", 
  "The final stage of childhood—memories begin to belong to a group."
];

let menuAlpha = 0;
let menuFadeIn = false;
let menuYPositions = [];
let menuTargetYPositions = [];
let menuLineHeight = 60;
let menuStartY;

// 线条绘制
let linePoints = [];
let currentLineIndex = 0;
let lineDrawSpeed = 5;
let lineAlpha = 0;
let lineFadeIn = false;
let lineDrawingComplete = false;

// 图片文字显示
let showImageText = false;
let textFadeAlpha = 0;
let currentImageText = "";

// 最终文字
let finalText = "Those fragments piece together our unique chilhood...";
let finalTextAlpha = 0;
let finalTextFadeIn = false;
let finalTextFadeOut = false;

// 字体
let starBlushFont;
let fzssFont;

class Particle {
  constructor(sx, sy, tx, ty, col) {
    this.pos = createVector(sx, sy);
    this.target = createVector(tx, ty);
    this.color = col;
    this.vel = createVector(0, 0);
    this.exploding = false;
    this.size = random(2, 5);
  }

  update() {
    if (this.exploding) {
      this.pos.add(this.vel);
      this.vel.mult(0.95);
    } else {
      let dir = p5.Vector.sub(this.target, this.pos);
      this.pos.add(dir.mult(0.05));
    }
  }

  explode() {
    this.exploding = true;
    this.vel = p5.Vector.random2D().mult(random(3, 6));
  }

  show() {
    noStroke();
    fill(this.color);
    ellipse(this.pos.x, this.pos.y, this.size);
  }
}

class Block {
  constructor(img, sx, sy, size) {
    this.img = img.get(sx, sy, size, size);
    this.sx = sx;
    this.sy = sy;
    this.size = size;
    this.offsetX = width / 2 - img.width / 2;
    this.offsetY = height / 2 - img.height / 2;
    this.pos = createVector(random(width), random(height));
    this.target = createVector(this.offsetX + this.sx, this.offsetY + this.sy);
    this.assembled = false;
  }

  assemble() {
    let dir = p5.Vector.sub(this.target, this.pos);
    if (dir.mag() < 1) {
      this.assembled = true;
    }
    this.pos.add(dir.mult(0.1));
  }

  show() {
    imageMode(CORNER);
    image(this.img, this.pos.x, this.pos.y, this.size, this.size);
  }
}

function preload() {
  images.push(loadImage('11.jpg'));
  images.push(loadImage('12.jpg'));
  images.push(loadImage('13.jpg'));
  images.push(loadImage('14.jpg'));
  images.push(loadImage('15.jpg'));
  images.push(loadImage('16.jpg'));
  
  // starBlushFont = loadFont('StarBlushSerifBold.ttf');
  // fzssFont = loadFont('FangZhengShuSongJianTi.ttf');
}

function setup() {
  createCanvas(960, 540);
  imageMode(CENTER);
  
  // Initialize menu positions
  menuStartY = height;
  for (let i = 0; i < menuLines.length; i++) {
    menuYPositions[i] = menuStartY + i * menuLineHeight;
    menuTargetYPositions[i] = height/2 - (menuLines.length * menuLineHeight)/2 + i * menuLineHeight;
  }
  
  prepareParticles(images[2]);
  prepareBlocks(images[4]);
  prepareLineDrawing(images[5]);
  
  textSize(25);
  textAlign(CENTER, CENTER);
}

function draw() {
  background(30);

  // 初始文本介绍阶段
  if (currentStage === -2) {
    drawTextIntro();
    return;
  }
  
  // 六行文字选择菜单
  if (currentStage === -1) {
    drawMenu();
    return;
  }

  // 图片展示阶段
  if (currentStage >= 0 && currentStage <= 5) {
    drawImageStage();
  }
  
  // 最终文字显示
  if (finalTextFadeIn || finalTextFadeOut) {
    drawFinalText();
  }
}

function drawTextIntro() {
  background(0);
  
  if (textFadingOut) {
    let allInvisible = true;
    for (let i = 0; i < textAlpha.length; i++) {
      textAlpha[i] = max(0, textAlpha[i] - 10);
      if (textAlpha[i] > 0) allInvisible = false;
    }
    
    if (allInvisible) {
      currentStage = -1;
      menuFadeIn = true;
    }
    
    for (let i = 0; i < textLines.length; i++) {
      if (textAlpha[i] > 0) {
        fill(255, textAlpha[i]);
        setTextFontForLine(i);
        text(textLines[i], width/2, height/3 + (i * 80));
      }
    }
    return;
  }
  
  for (let i = 0; i <= currentTextLine; i++) {
    if (textAlpha[i] > 0) {
      fill(255, textAlpha[i]);
      setTextFontForLine(i);
      text(textLines[i], width/2, height/3 + (i * 80));
    }
  }
  
  if (!textIntroComplete && currentTextLine < textLines.length) {
    textAlpha[currentTextLine] += textFadeSpeed;
    
    if (textAlpha[currentTextLine] >= 255) {
      textAlpha[currentTextLine] = 255;
      
      if (currentTextLine < textLines.length - 1) {
        if (nextLineTime === 0) {
          nextLineTime = millis() + 1000;
        } else if (millis() >= nextLineTime) {
          currentTextLine++;
          nextLineTime = 0;
        }
      } else {
        textIntroComplete = true;
      }
    }
  }
}

function drawMenu() {
  background(0);
  
  // 菜单淡入效果
  if (menuFadeIn) {
    menuAlpha = min(255, menuAlpha + 5);
    if (menuAlpha === 255) {
      menuFadeIn = false;
    }
  }
  
  // 更新菜单位置
  for (let i = 0; i < menuYPositions.length; i++) {
    let dy = (menuTargetYPositions[i] - menuYPositions[i]) * 0.1;
    menuYPositions[i] += dy;
    
    if (abs(dy) < 0.1) {
      menuYPositions[i] = menuTargetYPositions[i];
    }
  }
  
  // 绘制菜单项
  for (let i = 0; i < menuLines.length; i++) {
    let y = menuYPositions[i];
    let hover = dist(mouseX, mouseY, width/2, y) < 30;
    
    if (hover) {
      fill(255, 200, 0, menuAlpha);
      stroke(255, 200, 0, menuAlpha);
      strokeWeight(2);
    } else {
      fill(255, menuAlpha);
      noStroke();
    }
    
    textSize(28);
    textFont(starBlushFont || 'serif');
    text(menuLines[i], width/2, y);
    
    if (hover) {
      noFill();
      ellipse(width/2 - textWidth(menuLines[i])/2 - 20, y, 10, 10);
      ellipse(width/2 + textWidth(menuLines[i])/2 + 20, y, 10, 10);
    }
  }
}

function drawImageStage() {
  // 阶段0: 第一张图片
  if (currentStage === 0) {
    imageMode(CENTER);
    tint(255, 255 - fadeAmount);
    image(images[0], width / 2, height / 2);
    
    // 绘制图片文字
    if (showImageText) {
      textFadeAlpha = min(255, textFadeAlpha + 5);
      fill(255, textFadeAlpha - fadeAmount);
      noStroke();
      textSize(24);
      textAlign(CENTER, CENTER);
      text(currentImageText, width/2, height - 50);
    }
    
    if (fading) {
      fadeAmount += 5;
      if (fadeAmount >= 255) {
        returnToMenu();
      }
    }
  }
  
  // 阶段1: 第二张图片
  else if (currentStage === 1) {
    imageMode(CENTER);
    tint(255, 255 - fadeAmount);
    image(images[1], width/2, height/2);
    
    if (showImageText) {
      textFadeAlpha = min(255, textFadeAlpha + 5);
      fill(255, textFadeAlpha - fadeAmount);
      noStroke();
      textSize(24);
      textAlign(CENTER, CENTER);
      text(currentImageText, width/2, height - 50);
    }
    
    if (fading) {
      fadeAmount += 5;
      if (fadeAmount >= 255) {
        returnToMenu();
      }
    }
  }
  
  // 阶段2: 粒子效果
  else if (currentStage === 2) {
    drawParticles();
    
    if (showImageText) {
      textFadeAlpha = min(255, textFadeAlpha + 5);
      fill(255, textFadeAlpha - fadeAmount);
      noStroke();
      textSize(24);
      textAlign(CENTER, CENTER);
      text(currentImageText, width/2, height - 50);
    }
    
    if (fading) {
      fadeAmount += 5;
      if (fadeAmount >= 255) {
        returnToMenu();
      }
    }
  }
  
  // 阶段3: 海报化效果
  else if (currentStage === 3) {
    imageMode(CENTER);
    tint(255, 255 - fadeAmount);
    image(images[3], width / 2, height / 2);
    filter(POSTERIZE, 3);
    
    if (showImageText) {
      textFadeAlpha = min(255, textFadeAlpha + 5);
      fill(255, textFadeAlpha - fadeAmount);
      noStroke();
      textSize(24);
      textAlign(CENTER, CENTER);
      text(currentImageText, width/2, height - 50);
    }
    
    if (fading) {
      fadeAmount += 5;
      if (fadeAmount >= 255) {
        returnToMenu();
      }
    }
  }
  
  // 阶段4: 方块组装 (第五张图片)
  else if (currentStage === 4) {
    // 只绘制方块碎片
    for (let b of blocks) {
      if (blockAssembled) b.assemble();
      b.show();
    }
    
    // 如果方块已经组装完成，显示文字
    if (blockAssembled) {
      if (showImageText) {
        textFadeAlpha = min(255, textFadeAlpha + 5);
        fill(255, textFadeAlpha - fadeAmount);
        noStroke();
        textSize(24);
        textAlign(CENTER, CENTER);
        text(currentImageText, width/2, height - 50);
      }
      
      // 如果文字和图片都显示了，点击后返回菜单
      if (fading) {
        fadeAmount += 5;
        if (fadeAmount >= 255) {
          returnToMenu();
        }
      }
    }
  }
  
  // 阶段5: 线条绘制和淡出
  else if (currentStage === 5) {
    if (lineFadeIn) {
      background(30);
      drawLineImage();
      
      tint(255, lineAlpha - fadeOutAlpha);
      image(images[5], width/2, height/2);
      lineAlpha += 2;
      if (lineAlpha >= 255) {
        lineFadeIn = false;
      }
    } else {
      imageMode(CENTER);
      tint(255, 255 - fadeOutAlpha);
      image(images[5], width/2, height/2);
    }

    if (showImageText) {
      textFadeAlpha = min(255, textFadeAlpha + 5);
      fill(255, textFadeAlpha - fadeOutAlpha);
      noStroke();
      textSize(24);
      textAlign(CENTER, CENTER);
      text(currentImageText, width/2, height - 50);
    }

    if (fadeOut) {
      fadeOutAlpha += 5;
      if (fadeOutAlpha >= 255) {
        fadeOut = false;
        finalTextFadeIn = true;
      }
    }
  }
}

function drawFinalText() {
  if (finalTextFadeIn) {
    finalTextAlpha = min(255, finalTextAlpha + 5);
    fill(255, finalTextAlpha);
    textSize(32);
    textAlign(CENTER, CENTER);
    textFont(starBlushFont || 'serif');
    text(finalText, width/2, height/2);
    
    if (finalTextAlpha >= 255) {
      finalTextFadeIn = false;
    }
  } else if (finalTextFadeOut) {
    finalTextAlpha = max(0, finalTextAlpha - 3);
    fill(255, finalTextAlpha);
    textSize(32);
    textAlign(CENTER, CENTER);
    textFont(starBlushFont || 'serif');
    text(finalText, width/2, height/2);
    
    if (finalTextAlpha <= 0) {
      finalTextFadeOut = false;
      returnToInitialState();
    }
  }
}

function returnToInitialState() {
  currentStage = -2;
  textIntroComplete = false;
  textFadingOut = false;
  currentTextLine = 0;
  nextLineTime = 0;
  
  for (let i = 0; i < textAlpha.length; i++) {
    textAlpha[i] = 0;
  }
}

function mousePressed() {
  // 初始文本介绍阶段
  if (currentStage === -2) {
    if (textIntroComplete && !textFadingOut) {
      textFadingOut = true;
    }
    return;
  }
  
  // 六行文字选择菜单
  if (currentStage === -1) {
    for (let i = 0; i < menuLines.length; i++) {
      if (dist(mouseX, mouseY, width/2, menuYPositions[i]) < 30) {
        currentStage = i;
        
        // 准备特定效果
        if (currentStage === 2) prepareParticles(images[2]);
        if (currentStage === 4) prepareBlocks(images[4]);
        if (currentStage === 5) prepareLineDrawing(images[5]);
        return;
      }
    }
    return;
  }

  if (transitioning) return;

  // 图片阶段点击处理
  if (currentStage >= 0 && currentStage <= 5) {
    // 第五张图片的特殊处理
    if (currentStage === 4) {
      // 第一次点击：开始组装方块
      if (!blockAssembled) {
        blockAssembled = true;
        return;
      }
      // 第二次点击：显示文字
      else if (blockAssembled && !showImageText) {
        showImageText = true;
        currentImageText = imageTexts[currentStage];
        textFadeAlpha = 0;
        return;
      }
      // 第三次点击：返回菜单
      else if (blockAssembled && showImageText) {
        fading = true;
        fadeAmount = 0;
        return;
      }
    }
    
    // 其他图片的正常处理
    else {
      // 如果已经显示文字，则点击返回菜单或淡出
      if (showImageText) {
        // 特殊处理第六张图片（淡出效果）
        if (currentStage === 5) {
          fadeOut = true;
          fadeOutAlpha = 0;
        } 
        // 特殊处理第三张图片（粒子效果）
        else if (currentStage === 2) {
          for (let p of particles) {
            p.explode();
          }
          transitioning = true;
          setTimeout(() => {
            returnToMenu();
            transitioning = false;
          }, 1500);
        }
        // 其他图片正常返回菜单
        else {
          fading = true;
          fadeAmount = 0;
        }
        return;
      }
      
      // 第一次点击显示文字
      if (!showImageText) {
        showImageText = true;
        currentImageText = imageTexts[currentStage];
        textFadeAlpha = 0;
        return;
      }
    }
  }
  
  // 最终文字点击处理
  if ((finalTextFadeIn && !finalTextFadeOut) || (finalTextAlpha === 255 && !finalTextFadeIn && !finalTextFadeOut)) {
    finalTextFadeOut = true;
  }
}

function returnToMenu() {
  // 重置所有效果状态
  fading = false;
  fadeAmount = 0;
  nextStageReady = false;
  blockAssembled = false;
  blockClickedOnce = false;
  fadeOut = false;
  fadeOutAlpha = 0;
  lineFadeIn = false;
  lineAlpha = 0;
  lineDrawingComplete = false;
  currentLineIndex = 0;
  showImageText = false;
  textFadeAlpha = 0;
  currentImageText = "";
  
  // 返回菜单
  currentStage = -1;
  menuAlpha = 0;
  menuFadeIn = true;
  
  // 重置菜单位置动画
  for (let i = 0; i < menuLines.length; i++) {
    menuYPositions[i] = menuStartY + i * menuLineHeight;
  }
}

function prepareLineDrawing(img) {
  img.resize(width, height);
  img.loadPixels();
  linePoints = [];
  
  for (let y = 0; y < img.height; y += 2) {
    for (let x = 0; x < img.width; x += 2) {
      let index = (y * img.width + x) * 4;
      let r = img.pixels[index];
      let g = img.pixels[index + 1];
      let b = img.pixels[index + 2];
      let brightness = (r + g + b) / 3;
      
      if (x > 0 && y > 0 && x < img.width-1 && y < img.height-1) {
        let leftIndex = (y * img.width + (x-1)) * 4;
        let leftBrightness = (img.pixels[leftIndex] + img.pixels[leftIndex+1] + img.pixels[leftIndex+2]) / 3;
        
        let topIndex = ((y-1) * img.width + x) * 4;
        let topBrightness = (img.pixels[topIndex] + img.pixels[topIndex+1] + img.pixels[topIndex+2]) / 3;
        
        if (Math.abs(brightness - leftBrightness) > 20 || Math.abs(brightness - topBrightness) > 20) {
          linePoints.push({
            x: x,
            y: y,
            color: color(r, g, b)
          });
        }
      }
    }
  }
  
  for (let i = linePoints.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [linePoints[i], linePoints[j]] = [linePoints[j], linePoints[i]];
  }
}

function drawLineImage() {
  if (currentLineIndex < linePoints.length) {
    let end = min(currentLineIndex + lineDrawSpeed, linePoints.length);
    for (let i = currentLineIndex; i < end; i++) {
      let pt = linePoints[i];
      stroke(pt.color);
      strokeWeight(1);
      let angle = random(TWO_PI);
      let len = random(1, 3);
      line(pt.x, pt.y, pt.x + cos(angle) * len, pt.y + sin(angle) * len);
    }
    currentLineIndex = end;
  } else if (!lineDrawingComplete) {
    lineDrawingComplete = true;
    lineFadeIn = true;
    lineAlpha = 0;
  }
}

function setTextFontForLine(lineIndex) {
  if (lineIndex === 2) {
    textFont(fzssFont || 'serif');
    textSize(42);
  } else {
    textFont(starBlushFont || 'serif');
    textSize(35);
  }
}

function prepareParticles(img) {
  img.resize(width, height);
  img.loadPixels();
  particles = [];

  let cols = width / density;
  let rows = height / density;

  for (let y = 0; y < rows; y++) {
    for (let x = 0; x < cols; x++) {
      let px = x * density;
      let py = y * density;
      let index = (px + py * img.width) * 4;
      let r = img.pixels[index];
      let g = img.pixels[index + 1];
      let b = img.pixels[index + 2];
      let col = color(r, g, b);
      particles.push(new Particle(random(width), random(height), px, py, col));
    }
  }
}

function drawParticles() {
  for (let p of particles) {
    p.update();
    p.show();
  }
}

function prepareBlocks(img) {
  img.resize(width, height);
  blocks = [];

  let blockSize = 40;
  let cols = ceil(width / blockSize);
  let rows = ceil(height / blockSize);

  for (let y = 0; y < rows; y++) {
    for (let x = 0; x < cols; x++) {
      let sx = x * blockSize;
      let sy = y * blockSize;
      sx = min(sx, img.width - blockSize);
      sy = min(sy, img.height - blockSize);
      blocks.push(new Block(img, sx, sy, blockSize));
    }
  }
}

function drawBlocks() {
  for (let b of blocks) {
    if (blockAssembled) b.assemble();
    b.show();
  }
}