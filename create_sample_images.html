<!DOCTYPE html>
<html>
<head>
    <title>创建示例图片</title>
</head>
<body>
    <canvas id="canvas" width="400" height="300"></canvas>
    <script>
        // 创建示例图片的脚本
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 图片主题和颜色
        const themes = [
            { name: '玩具王国', colors: ['#FF6B9D', '#F7931E', '#FFD93D'] },
            { name: '草地白裙梦', colors: ['#90EE90', '#FFFFFF', '#87CEEB'] },
            { name: '门后的眼睛', colors: ['#8B4513', '#2F4F4F', '#FFE4B5'] },
            { name: '奔跑的糖果女孩', colors: ['#FF69B4', '#FFA500', '#32CD32'] },
            { name: '冰淇淋友谊夏日', colors: ['#FFB6C1', '#87CEFA', '#FFFFE0'] },
            { name: '教室里的我们', colors: ['#F5DEB3', '#8FBC8F', '#DDA0DD'] }
        ];
        
        function createImage(index) {
            const theme = themes[index];
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, theme.colors[0]);
            gradient.addColorStop(0.5, theme.colors[1]);
            gradient.addColorStop(1, theme.colors[2]);
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 添加一些装饰性元素
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            for (let i = 0; i < 20; i++) {
                const x = Math.random() * canvas.width;
                const y = Math.random() * canvas.height;
                const size = Math.random() * 20 + 5;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // 添加主题文字
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(theme.name, canvas.width / 2, canvas.height / 2);
            
            // 下载图片
            const link = document.createElement('a');
            link.download = `1${index + 1}.jpg`;
            link.href = canvas.toDataURL('image/jpeg', 0.8);
            link.click();
        }
        
        // 创建所有图片
        for (let i = 0; i < 6; i++) {
            setTimeout(() => createImage(i), i * 1000);
        }
        
        document.body.innerHTML += '<p>正在生成示例图片，请稍等...</p>';
    </script>
</body>
</html>
