<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>童年记忆 - Whispers of Memory</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #1e1e1e;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        
        #canvas-container {
            border: 2px solid #333;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }
        
        .info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            max-width: 300px;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .info h3 {
            margin: 0 0 10px 0;
            color: #ffcc00;
        }
        
        .error {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid #ff6b6b;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="info">
        <h3>童年记忆 - 交互式艺术作品</h3>
        <p><strong>操作说明：</strong></p>
        <ul>
            <li>点击屏幕进入下一阶段</li>
            <li>在菜单中点击选择不同的记忆</li>
            <li>每张图片都有独特的交互效果</li>
        </ul>
        
        <div class="error">
            <strong>注意：</strong> 程序需要以下图片文件：
            <br>• 11.jpg - 16.jpg (6张图片)
            <br>• 请将图片放在同一目录下
        </div>
    </div>
    
    <div id="canvas-container"></div>
    
    <script src="test.js"></script>
</body>
</html>
