from PIL import Image, ImageDraw, ImageFont
import random

def create_sample_image(filename, theme, colors, size=(400, 300)):
    """创建示例图片"""
    # 创建图片
    img = Image.new('RGB', size, colors[0])
    draw = ImageDraw.Draw(img)
    
    # 创建渐变背景效果
    for y in range(size[1]):
        ratio = y / size[1]
        r = int(colors[0][0] * (1 - ratio) + colors[1][0] * ratio)
        g = int(colors[0][1] * (1 - ratio) + colors[1][1] * ratio)
        b = int(colors[0][2] * (1 - ratio) + colors[1][2] * ratio)
        draw.line([(0, y), (size[0], y)], fill=(r, g, b))
    
    # 添加一些装饰性圆圈
    for _ in range(15):
        x = random.randint(0, size[0])
        y = random.randint(0, size[1])
        radius = random.randint(10, 30)
        color = colors[random.randint(0, len(colors)-1)]
        alpha = random.randint(50, 150)
        
        # 创建半透明圆圈
        circle_img = Image.new('RGBA', size, (0, 0, 0, 0))
        circle_draw = ImageDraw.Draw(circle_img)
        circle_draw.ellipse([x-radius, y-radius, x+radius, y+radius], 
                          fill=(*color, alpha))
        img = Image.alpha_composite(img.convert('RGBA'), circle_img).convert('RGB')
    
    # 添加主题文字
    draw = ImageDraw.Draw(img)
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    # 计算文字位置
    bbox = draw.textbbox((0, 0), theme, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    # 绘制文字阴影
    draw.text((x+2, y+2), theme, fill=(0, 0, 0, 128), font=font)
    # 绘制文字
    draw.text((x, y), theme, fill=(255, 255, 255), font=font)
    
    # 保存图片
    img.save(filename, 'JPEG', quality=85)
    print(f"已创建: {filename}")

# 定义主题和颜色
themes_data = [
    ("Memory 1: Toys", [(255, 107, 157), (247, 147, 30), (255, 217, 61)]),
    ("Memory 2: White Dress", [(144, 238, 144), (255, 255, 255), (135, 206, 235)]),
    ("Memory 3: Behind Door", [(139, 69, 19), (47, 79, 79), (255, 228, 181)]),
    ("Memory 4: Candy Girl", [(255, 105, 180), (255, 165, 0), (50, 205, 50)]),
    ("Memory 5: Ice Cream", [(255, 182, 193), (135, 206, 250), (255, 255, 224)]),
    ("Memory 6: Classroom", [(245, 222, 179), (143, 188, 143), (221, 160, 221)])
]

# 创建所有图片
for i, (theme, colors) in enumerate(themes_data):
    filename = f"1{i+1}.jpg"
    create_sample_image(filename, theme, colors)

print("所有示例图片创建完成！")
